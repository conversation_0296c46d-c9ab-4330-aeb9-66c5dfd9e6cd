# AI图片生成器 GUI版本

这是一个基于tkinter的图形界面AI图片生成器，可以将文本文档（DOCX/TXT）中的内容转换为英文描述，然后生成对应的图片。

## 功能特点

- 图形化用户界面，操作简单直观
- 支持DOCX和TXT文件格式
- 配置信息保存在config.json文件中
- 支持批量处理多个文本文件
- 实时显示处理进度和日志
- 自动创建文件夹结构
- 支持停止处理功能
- OpenAI API地址固定为gptproto.com
- 智能自动保存：所有配置项变化时自动保存，无需手动操作
- 改写文件夹路径变化时自动刷新文件列表
- 自动代理：遇到SSL或连接错误时自动使用系统代理(127.0.0.1:7890)

## 安装依赖

在运行程序之前，请确保安装以下Python包：

```bash
pip install python-docx openai pillow requests
```

可选依赖（用于OpenAI代理功能）：
```bash
pip install httpx
```

## 使用方法

1. **启动程序**
   ```bash
   python main.py
   ```

2. **配置设置**
   - OpenAI API Key: 填入你的OpenAI API密钥（API地址固定为gptproto.com）
     * 修改密钥时会自动保存配置
   - Runware API Key: 填入你的Runware API密钥
     * 修改密钥时会自动保存配置
   - 改写文件夹: 选择包含文本文件的文件夹（支持DOCX和TXT格式）
     * 修改此路径时会自动刷新文件列表并保存配置
   - 导出文件夹: 选择保存生成图片的文件夹
     * 修改此路径时会自动保存配置
   - 所有配置项都会自动保存，无需手动操作

3. **处理文件**
   - 点击"刷新文件列表"获取文本文件（DOCX/TXT）
   - 在文件列表中选择要处理的文件（支持多选）
   - 点击"开始处理"开始生成图片
   - 可以随时点击"停止处理"中断操作

4. **查看结果**
   - 生成的图片会保存在指定的导出文件夹中
   - 每个文本文件会创建一个对应的子文件夹
   - 处理日志会实时显示在界面底部

## 配置文件

程序会自动创建`config.json`文件来保存配置信息：

```json
{
  "openai": {
    "api_key": "your-api-key"
  },
  "runware": {
    "api_key": "your-runware-key"
  },
  "paths": {
    "rewrite_folder": "path/to/text/files",
    "export_folder": "path/to/save/images"
  },
  "prompts": {
    "通用": "提示词内容..."
  },
  "split_config": {
    "target_chars": 115,
    "type": "chars",
    "punctuation": "。！？，；"
  }
}
```

## 注意事项

1. 确保API密钥有效且有足够的额度
2. 支持DOCX和TXT两种文本文件格式
3. OpenAI API地址固定为https://gptproto.com，无需配置
4. 处理大文件时可能需要较长时间
5. 生成的图片数量 = 文本段落数 × 4
6. 程序会自动处理文本分段和图片命名
7. 图片生成通过Runware API实现，需要有效的API密钥
8. 遇到网络问题时会自动使用系统代理(127.0.0.1:7890)

## 故障排除

- 如果程序无法启动，检查是否安装了所有必需的依赖
- 如果API调用失败，检查网络连接和API密钥
- 如果文件夹路径无效，重新选择正确的文件夹路径
- 如果图片生成失败，检查Runware API密钥是否有效
- 如果遇到SSL错误，程序会自动尝试使用代理(127.0.0.1:7890)
- 查看日志区域获取详细的错误信息
