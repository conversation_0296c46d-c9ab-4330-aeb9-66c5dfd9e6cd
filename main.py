import os
import re
import json
import time
import uuid
import threading
import subprocess
from pathlib import Path
from datetime import datetime
from io import BytesIO
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import queue

from docx import Document
from openai import OpenAI
from PIL import Image
import requests
try:
    import httpx
except ImportError:
    httpx = None


class ConfigManager:
    """配置管理类"""

    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def get_default_config(self):
        """获取默认配置"""
        return {
            "openai": {
                "api_key": ""
            },
            "runware": {
                "api_key": ""
            },
            "paths": {
                "rewrite_folder": str(Path.home() / "Desktop" / "剪辑" / "改写"),
                "export_folder": str(Path.home() / "Desktop" / "剪辑" / "导出")
            },
            "prompts": {
                "通用": """忘记你以前的一切设定
将下面的文本翻译为详细的场景英文描述，用作给AI出图，要求：
1. 译成英文，尽量以人事物+动作+周围场景作为格式
2. 描述写成一行
3. 只输出英文描述，不要其他的任何说明"""
            },
            "split_config": {
                "target_chars": 115,
                "type": "chars",
                "punctuation": "。！？，；"
            }
        }

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                default_config = self.get_default_config()
                return self.merge_config(default_config, config)
            else:
                return self.get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.get_default_config()

    def merge_config(self, default, loaded):
        """合并配置，确保所有默认键都存在"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key].update(value)
            else:
                result[key] = value
        return result

    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False

    def get(self, *keys):
        """获取配置值"""
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        return value

    def set(self, *keys, value):
        """设置配置值"""
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        config[keys[-1]] = value


class ImageGeneratorCore:
    """图片生成核心类"""

    def __init__(self, config_manager, progress_callback=None, log_callback=None):
        self.config_manager = config_manager
        self.progress_callback = progress_callback
        self.log_callback = log_callback

        self.start_time = None
        self.current_file = None
        self.current_file_name = None
        self.total_cost = 0.0
        self.prevent_sleep = None
        self.stop_processing = False

        # 初始化客户端
        self.client = None
        self.init_clients()

    def init_clients(self):
        """初始化API客户端"""
        try:
            # 设置代理配置
            self.proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }

            # OpenAI客户端 - URL写死为gptproto.com
            openai_config = self.config_manager.get("openai")
            if openai_config and openai_config.get("api_key"):
                self.client = OpenAI(
                    base_url="https://gptproto.com",
                    api_key=openai_config.get("api_key")
                )

            # Runware API Key（直接从配置获取）
            self.runware_api_key = self.config_manager.get("runware", "api_key")

        except Exception as e:
            self.log_message(f"初始化API客户端失败: {e}", "ERROR")

    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_text = f"[{current_time}] [{level}] {message}"
        print(log_text)
        if self.log_callback:
            self.log_callback(log_text)

    def update_progress(self, current, total, message=""):
        """更新进度"""
        if self.progress_callback:
            progress = (current / total) * 100 if total > 0 else 0
            self.progress_callback(progress, f"{current}/{total} {message}")

    def create_folders(self):
        """创建必要的文件夹"""
        try:
            rewrite_folder = self.config_manager.get("paths", "rewrite_folder")
            export_folder = self.config_manager.get("paths", "export_folder")

            if rewrite_folder:
                os.makedirs(rewrite_folder, exist_ok=True)
            if export_folder:
                os.makedirs(export_folder, exist_ok=True)

            return True
        except Exception as e:
            self.log_message(f"创建文件夹失败: {e}", "ERROR")
            return False

    def get_text_files(self):
        """获取文本文件列表（支持docx和txt）"""
        try:
            rewrite_folder = self.config_manager.get("paths", "rewrite_folder")
            if not rewrite_folder or not os.path.exists(rewrite_folder):
                return []

            text_files = []
            for file in os.listdir(rewrite_folder):
                if ((file.endswith('.docx') or file.endswith('.txt')) and
                    not file.startswith('.') and
                    not file.startswith('~$') and
                    not file.startswith('~') and
                    not file.endswith('.tmp')):
                    text_files.append(os.path.join(rewrite_folder, file))

            return sorted(text_files, key=lambda x: os.path.basename(x).lower())
        except Exception as e:
            self.log_message(f"获取文件列表失败: {e}", "ERROR")
            return []

    def split_into_paragraphs(self, text):
        """将文本分段"""
        try:
            config = self.config_manager.get("split_config")
            if not config:
                self.log_message("未找到分段配置", "ERROR")
                return None

            if config["type"] == "chars":
                # 中文模式：按字符数切割
                current_chars = 0
                paragraphs = []
                current_paragraph = ""

                for char in text:
                    current_paragraph += char
                    current_chars += 1

                    # 达到目标字符数后，寻找下一个标点
                    if current_chars >= config["target_chars"]:
                        # 在最后50个字符中查找标点
                        found_punct = False
                        for i in range(len(current_paragraph)-1, max(-1, len(current_paragraph)-50), -1):
                            if current_paragraph[i] in config["punctuation"]:
                                paragraphs.append(current_paragraph[:i+1].strip())
                                current_paragraph = current_paragraph[i+1:].strip()
                                current_chars = len(current_paragraph)
                                found_punct = True
                                break

                        # 如果没找到标点，强制切割
                        if not found_punct and current_chars > config["target_chars"] * 1.5:
                            paragraphs.append(current_paragraph.strip())
                            current_paragraph = ""
                            current_chars = 0

                # 添加最后一段
                if current_paragraph:
                    paragraphs.append(current_paragraph.strip())

                return paragraphs

            return None

        except Exception as e:
            self.log_message(f"分段处理出错: {e}", "ERROR")
            return None

    def process_segment_with_gpt(self, segment):
        """使用GPT处理段落"""
        try:
            if not segment or not self.client:
                return None

            # 检查是否需要停止处理
            if self.stop_processing:
                return None

            system_prompt = self.config_manager.get("prompts", "通用")
            if not system_prompt:
                self.log_message("未找到提示词配置", "ERROR")
                return None

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": segment}
            ]

            # 尝试调用OpenAI API，如果失败则使用代理
            try:
                # 设置较短的超时时间，避免长时间阻塞
                response = self.client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=messages,
                    temperature=0.7,
                    max_tokens=2048,
                    timeout=30  # 30秒超时
                )
            except Exception as e:
                if ("ssl" in str(e).lower() or "connection" in str(e).lower()) and httpx:
                    self.log_message(f"OpenAI API直连失败，尝试使用代理: {e}", "WARNING")
                    # 重新创建带代理的客户端，设置超时
                    proxy_client = httpx.Client(proxies=self.proxies, timeout=30.0)
                    openai_config = self.config_manager.get("openai")
                    temp_client = OpenAI(
                        base_url="https://gptproto.com",
                        api_key=openai_config.get("api_key"),
                        http_client=proxy_client
                    )
                    response = temp_client.chat.completions.create(
                        model="gpt-4o-mini",
                        messages=messages,
                        temperature=0.7,
                        max_tokens=2048
                    )
                    # 关闭代理客户端
                    proxy_client.close()
                else:
                    raise e

            if response and response.choices:
                prompt = response.choices[0].message.content.strip()
                # 添加高质量图片关键词
                quality_keywords = ", breathtaking photograph, cinematic lighting, 8k uhd, highly detailed, photorealistic, professional photography, masterpiece, sharp focus, high quality"
                prompt = prompt + quality_keywords
                return prompt

        except Exception as e:
            self.log_message(f"GPT处理失败: {e}", "ERROR")
            return None

    def generate_image(self, prompt):
        """使用Runware API生成图片"""
        if not self.runware_api_key:
            self.log_message("Runware API Key未配置，无法生成图片", "ERROR")
            return None, None

        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                # 检查是否需要停止处理
                if self.stop_processing:
                    return None, None

                # 添加请求延迟，避免API限流，但减少延迟时间
                time.sleep(1)  # 减少到1秒

                # API endpoint
                url = "https://api.runware.ai/v1/inference"

                # 请求头
                headers = {
                    "Authorization": f"Bearer {self.runware_api_key}",
                    "Content-Type": "application/json"
                }

                # 请求体
                data = [{
                    "taskType": "imageInference",
                    "taskUUID": str(uuid.uuid4()),
                    "model": "runware:101@1",
                    "positivePrompt": prompt,
                    "negativePrompt": "NSFW, nude, naked, blood, gore, violence, offensive, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry,letter",
                    "width": 1024,
                    "height": 576,
                    "steps": 20,
                    "scheduler": "FlowMatchEulerDiscreteScheduler",
                    "CFGScale": 3.5,
                    "outputType": "URL",
                    "outputFormat": "JPG",
                    "includeCost": True
                }]

                self.log_message("开始生成图片...")

                # 发送请求，先尝试不使用代理，设置较短超时
                try:
                    response = requests.post(url, headers=headers, json=data, timeout=60)  # 增加到60秒
                    response.raise_for_status()
                except (requests.exceptions.SSLError, requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                    self.log_message(f"直连失败，尝试使用代理: {e}", "WARNING")
                    # 使用代理重试
                    response = requests.post(url, headers=headers, json=data, proxies=self.proxies, timeout=60)
                    response.raise_for_status()

                # 解析响应
                result = response.json()

                if isinstance(result, dict) and 'data' in result and isinstance(result['data'], list):
                    for item in result['data']:
                        if item['taskType'] == 'imageInference':
                            # 计算费用
                            if 'cost' in item:
                                cost = item['cost']
                                self.total_cost += cost
                                self.log_message(f"本次生成费用: ${cost:.4f}")
                                rmb_cost = cost * 7.2
                                self.log_message(f"本次生成费用(人民币): ¥{rmb_cost:.2f}")

                            # 获取图片URL
                            task_id = item.get('taskUUID')
                            image_url = item.get('imageURL')
                            if task_id and image_url:
                                return task_id, image_url

                    self.log_message("API响应中没有找到有效的图片URL", "ERROR")
                else:
                    self.log_message("API响应格式错误", "ERROR")
                return None, None

            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    self.log_message(f"API请求失败，{retry_delay}秒后重试 ({attempt + 1}/{max_retries}): {str(e)}", "WARNING")
                    # 检查是否需要停止处理
                    for i in range(retry_delay):
                        if self.stop_processing:
                            return None, None
                        time.sleep(1)
                    continue
                self.log_message(f"API请求失败: {str(e)}", "ERROR")
                return None, None
            except Exception as e:
                self.log_message(f"生成图片失败: {str(e)}", "ERROR")
                return None, None

    def save_image(self, image_url, image_name):
        """保存图片到本地"""
        try:
            # 检查是否需要停止处理
            if self.stop_processing:
                return False

            # 创建保存目录
            save_folder = self.get_current_save_folder()
            if not save_folder:
                return False

            # 下载图片，先尝试不使用代理，设置较短超时
            try:
                response = requests.get(image_url, timeout=30, stream=True)  # 使用流式下载
                if response.status_code != 200:
                    self.log_message(f"下载图片失败: {response.status_code}", "ERROR")
                    return False
            except (requests.exceptions.SSLError, requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                self.log_message(f"直连下载失败，尝试使用代理: {e}", "WARNING")
                # 使用代理重试
                response = requests.get(image_url, proxies=self.proxies, timeout=30, stream=True)
                if response.status_code != 200:
                    self.log_message(f"代理下载图片失败: {response.status_code}", "ERROR")
                    return False

            # 保存图片，使用流式写入避免大文件阻塞
            image_path = os.path.join(save_folder, f"{image_name}.png")
            with open(image_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    # 检查是否需要停止处理
                    if self.stop_processing:
                        f.close()
                        # 删除未完成的文件
                        try:
                            os.remove(image_path)
                        except:
                            pass
                        return False
                    if chunk:
                        f.write(chunk)

            self.log_message(f"图片已保存: {image_path}")
            return True

        except Exception as e:
            self.log_message(f"保存图片失败: {e}", "ERROR")
            return False

    def read_file_content(self, file_path):
        """读取文件内容（支持docx和txt）"""
        try:
            self.log_message(f"尝试读取文件: {file_path}")

            if file_path.endswith('.txt'):
                # 读取txt文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        # 按行分割，过滤空行
                        paragraphs = [line.strip() for line in content.splitlines() if line.strip()]
                        return paragraphs
                    return []

            elif file_path.endswith('.docx'):
                # 读取docx文件
                doc = Document(file_path)
                paragraphs = []
                for para in doc.paragraphs:
                    text = para.text.strip()
                    if text:
                        paragraphs.append(text)
                return paragraphs

            else:
                self.log_message(f"不支持的文件格式: {file_path}", "ERROR")
                return None

        except Exception as e:
            self.log_message(f"读取文件出错: {e}", "ERROR")
            return None

    def get_current_save_folder(self):
        """获取当前保存文件夹"""
        try:
            if not self.current_file_name:
                self.log_message("当前没有处理的文件名", "ERROR")
                return None

            # 在导出文件夹下创建以文档名命名的子文件夹
            export_folder = self.config_manager.get("paths", "export_folder")
            save_folder = os.path.join(export_folder, self.current_file_name)

            # 如果文件夹不存在，则创建
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
                self.log_message(f"创建保存文件夹: {save_folder}")

            return save_folder

        except Exception as e:
            self.log_message(f"获取保存文件夹失败: {e}", "ERROR")
            return None

    def process_file(self, file_path):
        """处理单个文件"""
        try:
            if self.stop_processing:
                return False

            # 重置总费用
            self.total_cost = 0.0

            # 设置当前文件名和开始时间
            self.current_file = file_path
            self.current_file_name = os.path.splitext(os.path.basename(file_path))[0]
            self.start_time = time.time()

            self.log_message(f"开始处理文件: {file_path}")

            # 读取文件内容（支持docx和txt）
            paragraphs = self.read_file_content(file_path)
            if paragraphs is None:
                return False

            # 合并所有文本并进行分段
            full_text = ' '.join(paragraphs)
            # 清理文本：移除多余的空格和换行
            full_text = re.sub(r'\s+', ' ', full_text).strip()
            segments = self.split_into_paragraphs(full_text)

            if not segments:
                self.log_message("没有找到有效的文本段落", "ERROR")
                return False

            self.log_message(f"{os.path.basename(file_path)}已被分割成 {len(segments)} 段")

            # 创建保存文件夹
            save_folder = self.get_current_save_folder()
            if not save_folder:
                return False

            # 处理每个段落
            total_images = len(segments) * 4  # 每段生成4张图片
            current_image = 0

            for i, segment in enumerate(segments, 1):
                # 检查是否需要停止处理
                if self.stop_processing:
                    self.log_message("用户请求停止，正在退出处理循环...")
                    break

                self.update_progress(i, len(segments), f"处理第{i}段")

                # 使用GPT处理文本
                prompt = self.process_segment_with_gpt(segment)
                if not prompt:
                    self.log_message(f"第{i}段GPT处理失败，跳过", "WARNING")
                    continue

                # 为每个prompt生成4张图片
                for j in range(4):
                    # 检查是否需要停止处理
                    if self.stop_processing:
                        self.log_message("用户请求停止，正在退出图片生成循环...")
                        break

                    current_image += 1
                    self.update_progress(current_image, total_images, f"生成图片 {i}_{j+1}")

                    # 生成图片
                    task_id, image_url = self.generate_image(prompt)
                    if not task_id or not image_url:
                        self.log_message(f"图片 {i}_{j+1} 生成失败，跳过", "WARNING")
                        continue

                    # 保存图片，使用新的命名方式
                    image_name = f"{i}_{j+1}"  # 例如：1_1.png, 1_2.png, 1_3.png, 1_4.png
                    if not self.save_image(image_url, image_name):
                        self.log_message(f"图片 {i}_{j+1} 保存失败", "WARNING")
                        continue

                # 段落处理完成后短暂休息，让UI有机会响应
                time.sleep(0.1)

            # 显示总费用
            if self.total_cost > 0:
                self.log_message(f"总费用: ${self.total_cost:.4f}")
                rmb_total_cost = self.total_cost * 7.2
                self.log_message(f"总费用(人民币): ¥{rmb_total_cost:.2f}")

            return True

        except Exception as e:
            self.log_message(f"处理文件失败: {e}", "ERROR")
            return False

    def process_files(self, file_paths):
        """处理多个文件"""
        try:
            self.stop_processing = False

            # 启动防睡眠
            self.prevent_sleep = subprocess.Popen(['caffeinate'])
            self.log_message("已启动防睡眠模式")

            success_count = 0
            for i, file_path in enumerate(file_paths, 1):
                if self.stop_processing:
                    break

                self.log_message(f"正在处理第{i}/{len(file_paths)}个文件: {os.path.basename(file_path)}")
                if self.process_file(file_path):
                    success_count += 1

            self.log_message(f"处理完成，成功处理 {success_count}/{len(file_paths)} 个文件")
            return success_count

        except Exception as e:
            self.log_message(f"批量处理失败: {e}", "ERROR")
            return 0
        finally:
            # 关闭防睡眠
            if self.prevent_sleep:
                self.prevent_sleep.terminate()
                self.log_message("已关闭防睡眠模式")

    def stop(self):
        """停止处理"""
        self.stop_processing = True
        self.log_message("正在停止处理...")


class MainGUI:
    """主GUI界面类"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AI图片生成器")
        self.root.geometry("800x700")

        # 初始化配置管理器
        self.config_manager = ConfigManager()

        # 初始化图片生成器
        self.generator = None
        self.processing_thread = None
        self.message_queue = queue.Queue()

        # 标记是否正在初始化
        self.initializing = True

        # 创建GUI界面
        self.create_widgets()
        self.load_config_to_gui()

        # 初始化完成
        self.initializing = False

        # 启动消息处理
        self.process_messages()

    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # OpenAI配置
        ttk.Label(config_frame, text="OpenAI API Key:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.openai_key_var = tk.StringVar()
        self.openai_key_var.trace_add('write', self.on_api_key_changed)  # 监听变化
        ttk.Entry(config_frame, textvariable=self.openai_key_var, width=50, show="*").grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # Runware配置
        ttk.Label(config_frame, text="Runware API Key:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.runware_key_var = tk.StringVar()
        self.runware_key_var.trace_add('write', self.on_api_key_changed)  # 监听变化
        ttk.Entry(config_frame, textvariable=self.runware_key_var, width=50, show="*").grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # 文件夹配置
        ttk.Label(config_frame, text="改写文件夹:").grid(row=2, column=0, sticky=tk.W, pady=2)
        folder_frame1 = ttk.Frame(config_frame)
        folder_frame1.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        folder_frame1.columnconfigure(0, weight=1)

        self.rewrite_folder_var = tk.StringVar()
        self.rewrite_folder_var.trace_add('write', self.on_rewrite_folder_changed)  # 监听变化
        ttk.Entry(folder_frame1, textvariable=self.rewrite_folder_var).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(folder_frame1, text="浏览", command=self.browse_rewrite_folder).grid(row=0, column=1, padx=(5, 0))

        ttk.Label(config_frame, text="导出文件夹:").grid(row=3, column=0, sticky=tk.W, pady=2)
        folder_frame2 = ttk.Frame(config_frame)
        folder_frame2.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        folder_frame2.columnconfigure(0, weight=1)

        self.export_folder_var = tk.StringVar()
        self.export_folder_var.trace_add('write', self.on_export_folder_changed)  # 监听变化
        ttk.Entry(folder_frame2, textvariable=self.export_folder_var).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(folder_frame2, text="浏览", command=self.browse_export_folder).grid(row=0, column=1, padx=(5, 0))

        # 文件处理区域
        file_frame = ttk.LabelFrame(main_frame, text="文件处理", padding="10")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)

        # 文件列表
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)

        ttk.Label(list_frame, text="文本文件列表 (DOCX/TXT):").grid(row=0, column=0, sticky=tk.W)

        # 文件列表框架
        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        listbox_frame.columnconfigure(0, weight=1)

        self.file_listbox = tk.Listbox(listbox_frame, selectmode=tk.MULTIPLE, height=6)
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E))

        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)

        # 按钮区域
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))

        ttk.Button(button_frame, text="刷新文件列表", command=self.refresh_file_list).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="开始处理", command=self.start_processing).grid(row=0, column=1, padx=(0, 10))
        self.stop_button = ttk.Button(button_frame, text="停止处理", command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=2)

        # 进度和日志区域
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding="10")
        progress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.progress_label = ttk.Label(progress_frame, text="准备就绪")
        self.progress_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))

        # 日志区域
        ttk.Label(progress_frame, text="处理日志:").grid(row=2, column=0, sticky=tk.W)

        self.log_text = scrolledtext.ScrolledText(progress_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        progress_frame.rowconfigure(3, weight=1)

    def load_config_to_gui(self):
        """将配置加载到GUI界面"""
        self.openai_key_var.set(self.config_manager.get("openai", "api_key") or "")
        self.runware_key_var.set(self.config_manager.get("runware", "api_key") or "")
        self.rewrite_folder_var.set(self.config_manager.get("paths", "rewrite_folder") or "")
        self.export_folder_var.set(self.config_manager.get("paths", "export_folder") or "")

        # 刷新文件列表
        self.refresh_file_list()

    def on_rewrite_folder_changed(self, *args):
        """改写文件夹路径变化时的回调"""
        try:
            # 忽略未使用的参数
            _ = args
            # 如果正在初始化，不执行自动保存和刷新
            if hasattr(self, 'initializing') and self.initializing:
                return
            # 延迟执行，避免频繁触发
            self.root.after(500, self.auto_save_and_refresh)
        except Exception as e:
            print(f"文件夹变化回调出错: {e}")

    def on_export_folder_changed(self, *args):
        """导出文件夹路径变化时的回调"""
        try:
            # 忽略未使用的参数
            _ = args
            # 如果正在初始化，不执行自动保存
            if hasattr(self, 'initializing') and self.initializing:
                return
            # 延迟执行，避免频繁触发
            self.root.after(500, self.auto_save_config_only)
        except Exception as e:
            print(f"导出文件夹变化回调出错: {e}")

    def on_api_key_changed(self, *args):
        """API密钥变化时的回调"""
        try:
            # 忽略未使用的参数
            _ = args
            # 如果正在初始化，不执行自动保存
            if hasattr(self, 'initializing') and self.initializing:
                return
            # 延迟执行，避免频繁触发
            self.root.after(1000, self.auto_save_config_only)  # API密钥变化延迟更长
        except Exception as e:
            print(f"API密钥变化回调出错: {e}")

    def auto_save_and_refresh(self):
        """自动保存配置并刷新文件列表"""
        try:
            # 检查路径是否有效
            folder_path = self.rewrite_folder_var.get().strip()
            if folder_path and os.path.exists(folder_path):
                # 自动保存配置
                self.save_config_silently()
                # 刷新文件列表
                self.refresh_file_list()
        except Exception as e:
            print(f"自动保存和刷新出错: {e}")

    def auto_save_config_only(self):
        """仅自动保存配置"""
        try:
            # 自动保存配置
            self.save_config_silently()
        except Exception as e:
            print(f"自动保存配置出错: {e}")

    def save_config_silently(self):
        """静默保存配置（不显示提示）"""
        try:
            # 更新配置
            self.config_manager.set("openai", "api_key", value=self.openai_key_var.get())
            self.config_manager.set("runware", "api_key", value=self.runware_key_var.get())
            self.config_manager.set("paths", "rewrite_folder", value=self.rewrite_folder_var.get())
            self.config_manager.set("paths", "export_folder", value=self.export_folder_var.get())

            # 保存到文件
            self.config_manager.save_config()
            # 重新初始化生成器
            if self.generator:
                self.generator.init_clients()
                # 更新API key
                self.generator.runware_api_key = self.runware_key_var.get()
        except Exception as e:
            print(f"静默保存配置失败: {e}")

    def browse_rewrite_folder(self):
        """浏览改写文件夹"""
        folder = filedialog.askdirectory(title="选择改写文件夹")
        if folder:
            self.rewrite_folder_var.set(folder)

    def browse_export_folder(self):
        """浏览导出文件夹"""
        folder = filedialog.askdirectory(title="选择导出文件夹")
        if folder:
            self.export_folder_var.set(folder)

    def refresh_file_list(self):
        """刷新文件列表"""
        try:
            # 清空列表
            self.file_listbox.delete(0, tk.END)

            # 创建临时生成器来获取文件列表
            temp_generator = ImageGeneratorCore(self.config_manager)
            temp_generator.create_folders()
            text_files = temp_generator.get_text_files()

            # 添加文件到列表
            for file_path in text_files:
                filename = os.path.basename(file_path)
                self.file_listbox.insert(tk.END, filename)

            self.log_to_gui(f"找到 {len(text_files)} 个文本文件")

        except Exception as e:
            self.log_to_gui(f"刷新文件列表失败: {e}")

    def start_processing(self):
        """开始处理文件"""
        try:
            # 检查配置
            if not self.validate_config():
                return

            # 获取选中的文件
            selected_indices = self.file_listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("警告", "请选择要处理的文件")
                return

            # 获取文件路径
            temp_generator = ImageGeneratorCore(self.config_manager)
            text_files = temp_generator.get_text_files()
            selected_files = [text_files[i] for i in selected_indices]

            # 确认开始处理
            file_names = [os.path.basename(f) for f in selected_files]
            message = f"确定要处理以下 {len(selected_files)} 个文件吗？\n\n" + "\n".join(file_names)
            if not messagebox.askyesno("确认", message):
                return

            # 禁用开始按钮，启用停止按钮
            self.stop_button.config(state=tk.NORMAL)

            # 重置进度条
            self.progress_var.set(0)
            self.progress_label.config(text="准备开始处理...")

            # 清空日志
            self.log_text.delete(1.0, tk.END)

            # 在新线程中开始处理
            self.processing_thread = threading.Thread(
                target=self.process_files_thread,
                args=(selected_files,),
                daemon=True
            )
            self.processing_thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动处理失败: {e}")
            # 确保按钮状态正确
            self.stop_button.config(state=tk.DISABLED)

    def validate_config(self):
        """验证配置"""
        if not self.openai_key_var.get().strip():
            messagebox.showerror("错误", "请填写OpenAI API Key")
            return False

        if not self.runware_key_var.get().strip():
            messagebox.showerror("错误", "请填写Runware API Key")
            return False

        if not self.rewrite_folder_var.get().strip():
            messagebox.showerror("错误", "请选择改写文件夹")
            return False

        if not self.export_folder_var.get().strip():
            messagebox.showerror("错误", "请选择导出文件夹")
            return False

        return True

    def stop_processing(self):
        """停止处理"""
        try:
            if self.generator:
                self.generator.stop()
            self.stop_button.config(state=tk.DISABLED)
            self.progress_label.config(text="正在停止处理...")
            self.log_to_gui("用户请求停止处理，正在安全退出...")

            # 强制更新UI
            self.root.update_idletasks()
        except Exception as e:
            print(f"停止处理时出错: {e}")

    def process_files_thread(self, file_paths):
        """在线程中处理文件"""
        try:
            # 创建生成器
            self.generator = ImageGeneratorCore(
                self.config_manager,
                progress_callback=self.update_progress_gui,
                log_callback=self.log_to_gui
            )

            # 处理文件
            success_count = self.generator.process_files(file_paths)

            # 处理完成
            self.message_queue.put(("processing_complete", success_count, len(file_paths)))

        except Exception as e:
            self.message_queue.put(("error", f"处理过程中出错: {e}"))

    def update_progress_gui(self, progress, message):
        """更新进度条"""
        self.message_queue.put(("progress", progress, message))

    def log_to_gui(self, message):
        """添加日志到GUI"""
        self.message_queue.put(("log", message))

    def process_messages(self):
        """处理消息队列"""
        try:
            # 处理多个消息，但限制数量避免UI阻塞
            message_count = 0
            max_messages_per_cycle = 10

            while message_count < max_messages_per_cycle:
                try:
                    message_type, *args = self.message_queue.get_nowait()
                    message_count += 1

                    if message_type == "progress":
                        progress, message = args
                        self.progress_var.set(progress)
                        self.progress_label.config(text=message)
                        # 强制更新UI
                        self.root.update_idletasks()

                    elif message_type == "log":
                        message = args[0]
                        self.log_text.insert(tk.END, message + "\n")
                        self.log_text.see(tk.END)
                        # 限制日志行数，避免内存占用过多
                        lines = int(self.log_text.index('end-1c').split('.')[0])
                        if lines > 1000:  # 保持最多1000行日志
                            self.log_text.delete(1.0, f"{lines-800}.0")

                    elif message_type == "processing_complete":
                        success_count, total_count = args
                        self.stop_button.config(state=tk.DISABLED)
                        self.progress_var.set(100)
                        self.progress_label.config(text=f"处理完成: {success_count}/{total_count}")
                        # 延迟显示完成对话框，确保UI更新完成
                        self.root.after(100, lambda: messagebox.showinfo("完成", f"处理完成！\n成功处理 {success_count}/{total_count} 个文件"))

                    elif message_type == "error":
                        error_message = args[0]
                        self.stop_button.config(state=tk.DISABLED)
                        # 延迟显示错误对话框
                        self.root.after(100, lambda msg=error_message: messagebox.showerror("错误", msg))

                except queue.Empty:
                    break

        except Exception as e:
            print(f"处理消息时出错: {e}")

        # 更频繁地处理消息，提高UI响应性
        self.root.after(50, self.process_messages)  # 从100ms改为50ms

    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = MainGUI()
    app.run()
